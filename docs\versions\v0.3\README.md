# AI-FDB v0.3 - 工作空间管理

## 版本概述

v0.3版本在v0.2 AI核心模块基础上，实现工作空间管理功能。用户可以创建和管理多个工作空间，每个工作空间可以包含不同的项目和数据表，支持权限管理和协作功能。本版本为后续的数据表创建和数据管理提供基础架构。

## 🎯 可视化验证目标

完成v0.3版本后，用户可以：
1. **工作空间创建** - 创建新的工作空间，设置名称和描述
2. **工作空间列表** - 查看用户拥有的所有工作空间
3. **工作空间切换** - 在不同工作空间之间切换
4. **权限管理** - 设置工作空间的访问权限
5. **协作功能** - 邀请其他用户加入工作空间
6. **工作空间设置** - 配置工作空间的基本信息和偏好设置

## 📋 完整实现清单

### 数据库扩展
- [x] 创建数据表定义表
- [x] 创建字段定义表
- [x] 创建表模板表
- [x] 创建AI生成历史表

### 后端功能实现
- [x] 数据表CRUD API
- [x] 字段管理API
- [x] AI表结构生成服务
- [x] 模板管理API
- [x] 表结构验证服务

### 前端功能实现
- [x] 数据表列表页面
- [x] 可视化表设计器
- [x] 字段编辑器组件
- [x] AI助手面板
- [x] 模板选择器

### AI服务集成
- [x] OpenAI API集成
- [x] 表结构生成提示词
- [x] 字段推荐算法
- [x] 提示词模板管理

## 功能模块

### 1. 数据表管理
- 数据表创建和配置
- 表结构设计器
- 表信息编辑和删除
- 表结构版本管理

### 2. 字段管理
- 字段类型定义
- 字段属性配置
- 字段验证规则
- 字段排序和分组

### 3. AI辅助功能
- 智能表结构生成
- 字段推荐算法
- 提示词自动生成
- 抽取规则优化

## 技术实现

### 新增技术组件
- JSON Schema 验证
- 动态表单生成
- AI服务集成框架
- 表结构版本控制

### 核心特性
- 灵活的字段类型系统
- 可视化表设计器
- 智能推荐引擎
- 实时预览功能

## 数据库设计

### 新增表结构

#### data_tables 表
```sql
CREATE TABLE data_tables (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100),
    description TEXT,
    workspace_id BIGINT NOT NULL,
    table_schema JSON NOT NULL COMMENT '表结构定义',
    ai_config JSON COMMENT 'AI抽取配置',
    version INT DEFAULT 1,
    status ENUM('draft', 'active', 'archived') DEFAULT 'draft',
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_workspace_id (workspace_id),
    INDEX idx_name (name),
    INDEX idx_status (status),
    UNIQUE KEY uk_workspace_name (workspace_id, name)
);
```

#### table_fields 表
```sql
CREATE TABLE table_fields (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100),
    field_type ENUM('text', 'number', 'date', 'datetime', 'boolean', 'file', 'url', 'email', 'phone', 'select', 'multiselect') NOT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    is_unique BOOLEAN DEFAULT FALSE,
    field_order INT DEFAULT 0,
    default_value TEXT,
    field_options JSON COMMENT '字段选项配置',
    validation_rules JSON COMMENT '字段验证规则',
    extraction_prompt TEXT COMMENT 'AI抽取提示词',
    extraction_examples JSON COMMENT '抽取示例',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    UNIQUE KEY uk_table_field (table_id, field_name),
    INDEX idx_table_id (table_id),
    INDEX idx_field_order (field_order)
);
```

#### table_templates 表
```sql
CREATE TABLE table_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50),
    description TEXT,
    template_schema JSON NOT NULL,
    usage_count INT DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_category (category),
    INDEX idx_public (is_public),
    INDEX idx_usage_count (usage_count)
);
```

#### ai_generation_history 表
```sql
CREATE TABLE ai_generation_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT,
    generation_type ENUM('table_structure', 'field_suggestion', 'prompt_generation') NOT NULL,
    input_prompt TEXT NOT NULL,
    generated_result JSON NOT NULL,
    confidence_score DECIMAL(3,2),
    user_feedback ENUM('accepted', 'modified', 'rejected'),
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_table_id (table_id),
    INDEX idx_generation_type (generation_type),
    INDEX idx_created_at (created_at)
);
```

## 字段类型系统

### 基础字段类型

#### 文本类型 (text)
```json
{
  "type": "text",
  "maxLength": 255,
  "minLength": 0,
  "pattern": "regex_pattern",
  "placeholder": "请输入文本"
}
```

#### 数字类型 (number)
```json
{
  "type": "number",
  "min": 0,
  "max": 999999,
  "precision": 2,
  "unit": "元"
}
```

#### 日期类型 (date/datetime)
```json
{
  "type": "date",
  "format": "YYYY-MM-DD",
  "minDate": "2020-01-01",
  "maxDate": "2030-12-31"
}
```

#### 选择类型 (select/multiselect)
```json
{
  "type": "select",
  "options": [
    {"value": "option1", "label": "选项1"},
    {"value": "option2", "label": "选项2"}
  ],
  "allowCustom": false
}
```

#### 文件类型 (file)
```json
{
  "type": "file",
  "allowedTypes": ["pdf", "doc", "docx", "jpg", "png"],
  "maxSize": 10485760,
  "multiple": false
}
```

### 验证规则系统

```json
{
  "required": true,
  "unique": false,
  "custom": [
    {
      "rule": "email",
      "message": "请输入有效的邮箱地址"
    },
    {
      "rule": "phone",
      "message": "请输入有效的手机号码"
    }
  ]
}
```

## API接口

### 数据表管理接口

#### 获取数据表列表
```
GET /api/workspaces/{workspaceId}/tables
Authorization: Bearer {token}
Query Parameters:
  - page: int (默认0)
  - size: int (默认10)
  - search: string (可选)
  - status: string (可选)
```

#### 创建数据表
```
POST /api/workspaces/{workspaceId}/tables
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "string",
  "displayName": "string",
  "description": "string",
  "fields": [
    {
      "fieldName": "string",
      "displayName": "string",
      "fieldType": "text|number|date|boolean|file|select",
      "isRequired": boolean,
      "fieldOptions": object,
      "validationRules": object,
      "extractionPrompt": "string"
    }
  ]
}
```

#### AI生成表结构
```
POST /api/workspaces/{workspaceId}/tables/ai-generate
Authorization: Bearer {token}
Content-Type: application/json

{
  "tableName": "string",
  "description": "string",
  "category": "string",
  "sampleData": "string" (可选)
}
```

#### 获取表结构详情
```
GET /api/workspaces/{workspaceId}/tables/{tableId}
Authorization: Bearer {token}
```

#### 更新表结构
```
PUT /api/workspaces/{workspaceId}/tables/{tableId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "displayName": "string",
  "description": "string",
  "fields": array
}
```

### 字段管理接口

#### 添加字段
```
POST /api/tables/{tableId}/fields
Authorization: Bearer {token}
Content-Type: application/json

{
  "fieldName": "string",
  "displayName": "string",
  "fieldType": "string",
  "isRequired": boolean,
  "fieldOptions": object,
  "validationRules": object,
  "extractionPrompt": "string"
}
```

#### 更新字段
```
PUT /api/tables/{tableId}/fields/{fieldId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "displayName": "string",
  "fieldOptions": object,
  "validationRules": object,
  "extractionPrompt": "string"
}
```

#### 删除字段
```
DELETE /api/tables/{tableId}/fields/{fieldId}
Authorization: Bearer {token}
```

#### 字段排序
```
PUT /api/tables/{tableId}/fields/reorder
Authorization: Bearer {token}
Content-Type: application/json

{
  "fieldOrders": [
    {"fieldId": 1, "order": 0},
    {"fieldId": 2, "order": 1}
  ]
}
```

### 模板管理接口

#### 获取表模板
```
GET /api/templates
Authorization: Bearer {token}
Query Parameters:
  - category: string (可选)
  - search: string (可选)
```

#### 从模板创建表
```
POST /api/workspaces/{workspaceId}/tables/from-template
Authorization: Bearer {token}
Content-Type: application/json

{
  "templateId": number,
  "tableName": "string",
  "customizations": object
}
```

## AI服务集成

### 表结构生成服务

```java
@Service
public class TableGenerationService {
    
    public TableGenerationResult generateTableStructure(
        String tableName, 
        String description, 
        String category) {
        
        String prompt = buildTableGenerationPrompt(tableName, description, category);
        AIResponse response = aiClient.generateCompletion(prompt);
        
        return parseTableStructure(response.getContent());
    }
    
    private String buildTableGenerationPrompt(String name, String desc, String category) {
        return String.format(
            "根据以下信息生成数据表结构：\n" +
            "表名：%s\n" +
            "描述：%s\n" +
            "类别：%s\n" +
            "请生成合适的字段列表，包括字段名、类型、是否必填、验证规则等。\n" +
            "输出格式为JSON。",
            name, desc, category
        );
    }
}
```

### 字段推荐服务

```java
@Service
public class FieldRecommendationService {
    
    public List<FieldSuggestion> suggestFields(
        String tableName, 
        String tableDescription,
        List<String> existingFields) {
        
        String prompt = buildFieldSuggestionPrompt(
            tableName, tableDescription, existingFields);
        
        AIResponse response = aiClient.generateCompletion(prompt);
        return parseFieldSuggestions(response.getContent());
    }
    
    public String generateExtractionPrompt(
        String fieldName, 
        String fieldType, 
        String context) {
        
        return String.format(
            "为字段'%s'（类型：%s）生成AI抽取提示词。\n" +
            "上下文：%s\n" +
            "提示词应该清晰、准确，便于AI理解和抽取。",
            fieldName, fieldType, context
        );
    }
}
```

## 前端界面

### 新增页面
- 数据表列表页 (`/workspaces/{id}/tables`)
- 表结构设计器 (`/workspaces/{id}/tables/create`)
- 表详情页 (`/workspaces/{id}/tables/{tableId}`)
- 字段编辑器 (`/workspaces/{id}/tables/{tableId}/fields`)
- AI助手页面 (`/workspaces/{id}/tables/ai-assistant`)

### 核心组件

#### TableDesigner - 表设计器组件
```vue
<template>
  <div class="table-designer">
    <div class="designer-header">
      <el-input v-model="tableInfo.name" placeholder="表名" />
      <el-input v-model="tableInfo.description" placeholder="表描述" />
    </div>
    
    <div class="designer-body">
      <FieldList 
        :fields="fields" 
        @add-field="handleAddField"
        @edit-field="handleEditField"
        @delete-field="handleDeleteField"
        @reorder-fields="handleReorderFields"
      />
    </div>
    
    <div class="designer-sidebar">
      <AIAssistant 
        :table-info="tableInfo"
        @suggest-fields="handleFieldSuggestions"
        @generate-prompts="handlePromptGeneration"
      />
    </div>
  </div>
</template>
```

#### FieldEditor - 字段编辑器组件
```vue
<template>
  <el-dialog v-model="visible" title="编辑字段">
    <el-form :model="fieldForm" :rules="fieldRules">
      <el-form-item label="字段名" prop="fieldName">
        <el-input v-model="fieldForm.fieldName" />
      </el-form-item>
      
      <el-form-item label="显示名" prop="displayName">
        <el-input v-model="fieldForm.displayName" />
      </el-form-item>
      
      <el-form-item label="字段类型" prop="fieldType">
        <el-select v-model="fieldForm.fieldType">
          <el-option label="文本" value="text" />
          <el-option label="数字" value="number" />
          <el-option label="日期" value="date" />
          <el-option label="布尔" value="boolean" />
          <el-option label="文件" value="file" />
          <el-option label="选择" value="select" />
        </el-select>
      </el-form-item>
      
      <FieldOptions 
        :field-type="fieldForm.fieldType"
        v-model="fieldForm.fieldOptions"
      />
      
      <ValidationRules 
        v-model="fieldForm.validationRules"
      />
      
      <el-form-item label="抽取提示词">
        <el-input 
          v-model="fieldForm.extractionPrompt"
          type="textarea"
          :rows="3"
        />
        <el-button 
          @click="generatePrompt"
          size="small"
          type="primary"
          style="margin-top: 8px"
        >
          AI生成提示词
        </el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
```

#### AIAssistant - AI助手组件
```vue
<template>
  <div class="ai-assistant">
    <el-card header="AI助手">
      <el-button 
        @click="suggestFields"
        :loading="suggesting"
        type="primary"
        style="width: 100%; margin-bottom: 12px"
      >
        智能推荐字段
      </el-button>
      
      <el-button 
        @click="generatePrompts"
        :loading="generating"
        style="width: 100%; margin-bottom: 12px"
      >
        生成抽取提示词
      </el-button>
      
      <el-button 
        @click="optimizeStructure"
        :loading="optimizing"
        style="width: 100%"
      >
        优化表结构
      </el-button>
    </el-card>
    
    <el-card header="推荐模板" style="margin-top: 16px">
      <TemplateList 
        :templates="recommendedTemplates"
        @apply-template="handleApplyTemplate"
      />
    </el-card>
  </div>
</template>
```

## 业务流程

### 手动创建表流程
1. 用户进入表设计器
2. 输入表名和描述
3. 逐个添加字段
4. 配置字段属性和验证规则
5. 预览表结构
6. 保存表定义

### AI辅助创建流程
1. 用户输入表名和描述
2. AI分析并推荐字段结构
3. 用户确认或修改推荐结果
4. AI生成抽取提示词
5. 用户调整提示词
6. 保存完整表定义

### 字段管理流程
1. 选择要编辑的字段
2. 修改字段属性
3. 更新验证规则
4. 测试抽取提示词
5. 保存字段配置

## 测试用例

### 功能测试
- 表创建和编辑功能测试
- 字段类型和验证测试
- AI推荐功能测试
- 模板应用功能测试

### 性能测试
- 大量字段表结构加载测试
- AI服务响应时间测试
- 并发创建表测试

### 兼容性测试
- 不同浏览器兼容性测试
- 移动端响应式测试
- 字段类型兼容性测试

## 🚀 实施步骤

### 步骤1: 数据库扩展
```sql
-- 在v0.2基础上添加数据表相关表

-- 数据表定义表
CREATE TABLE data_tables (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100),
    description TEXT,
    workspace_id BIGINT NOT NULL,
    table_schema JSON NOT NULL COMMENT '表结构定义',
    ai_config JSON COMMENT 'AI抽取配置',
    version INT DEFAULT 1,
    status ENUM('draft', 'active', 'archived') DEFAULT 'draft',
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_workspace_id (workspace_id),
    INDEX idx_name (name),
    INDEX idx_status (status),
    UNIQUE KEY uk_workspace_name (workspace_id, name)
);

-- 字段定义表
CREATE TABLE table_fields (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100),
    field_type ENUM('text', 'number', 'date', 'datetime', 'boolean', 'file', 'url', 'email', 'phone', 'select', 'multiselect') NOT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    is_unique BOOLEAN DEFAULT FALSE,
    field_order INT DEFAULT 0,
    default_value TEXT,
    field_options JSON COMMENT '字段选项配置',
    validation_rules JSON COMMENT '字段验证规则',
    extraction_prompt TEXT COMMENT 'AI抽取提示词',
    extraction_examples JSON COMMENT '抽取示例',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    UNIQUE KEY uk_table_field (table_id, field_name),
    INDEX idx_table_id (table_id),
    INDEX idx_field_order (field_order)
);

-- 表模板表
CREATE TABLE table_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50),
    description TEXT,
    template_schema JSON NOT NULL,
    usage_count INT DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_category (category),
    INDEX idx_public (is_public),
    INDEX idx_usage_count (usage_count)
);

-- AI生成历史表
CREATE TABLE ai_generation_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT,
    generation_type ENUM('table_structure', 'field_suggestion', 'prompt_generation') NOT NULL,
    input_prompt TEXT NOT NULL,
    generated_result JSON NOT NULL,
    confidence_score DECIMAL(3,2),
    user_feedback ENUM('accepted', 'modified', 'rejected'),
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_table_id (table_id),
    INDEX idx_generation_type (generation_type),
    INDEX idx_created_at (created_at)
);
```

### 步骤2: AI服务配置
```yaml
# application.yml 添加AI服务配置
ai:
  openai:
    api-key: ${OPENAI_API_KEY}
    base-url: https://api.openai.com/v1
    model: gpt-3.5-turbo
    max-tokens: 2000
    temperature: 0.7

  baidu:
    api-key: ${BAIDU_API_KEY}
    secret-key: ${BAIDU_SECRET_KEY}
    base-url: https://aip.baidubce.com

  alibaba:
    api-key: ${ALIBABA_API_KEY}
    base-url: https://dashscope.aliyuncs.com
    model: qwen-turbo
```

### 步骤3: 后端核心代码实现
```java
// DataTableController.java - 数据表控制器
@RestController
@RequestMapping("/api/workspaces/{workspaceId}/tables")
@PreAuthorize("hasRole('USER')")
public class DataTableController {

    @Autowired
    private DataTableService dataTableService;

    @Autowired
    private AITableGenerationService aiService;

    @GetMapping
    @PreAuthorize("@workspacePermissionService.hasPermission(#workspaceId, authentication.name, 'VIEW')")
    public ResponseEntity<?> getTables(
        @PathVariable Long workspaceId,
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "10") int size) {

        Page<DataTable> tables = dataTableService.getWorkspaceTables(workspaceId, page, size);
        return ResponseEntity.ok(tables);
    }

    @PostMapping
    @PreAuthorize("@workspacePermissionService.hasPermission(#workspaceId, authentication.name, 'CREATE_TABLE')")
    public ResponseEntity<?> createTable(
        @PathVariable Long workspaceId,
        @RequestBody CreateTableRequest request,
        Authentication auth) {
        try {
            DataTable table = dataTableService.createTable(workspaceId, request, auth.getName());
            return ResponseEntity.ok(table);
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new ApiResponse("创建数据表失败: " + e.getMessage()));
        }
    }

    @PostMapping("/ai-generate")
    @PreAuthorize("@workspacePermissionService.hasPermission(#workspaceId, authentication.name, 'CREATE_TABLE')")
    public ResponseEntity<?> generateTableStructure(
        @PathVariable Long workspaceId,
        @RequestBody AIGenerateRequest request,
        Authentication auth) {
        try {
            TableGenerationResult result = aiService.generateTableStructure(
                request.getTableName(),
                request.getDescription(),
                request.getCategory()
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new ApiResponse("AI生成失败: " + e.getMessage()));
        }
    }
}

// AITableGenerationService.java - AI表生成服务
@Service
public class AITableGenerationService {

    @Autowired
    private OpenAIClient openAIClient;

    public TableGenerationResult generateTableStructure(String tableName, String description, String category) {
        String prompt = buildTableGenerationPrompt(tableName, description, category);

        try {
            String response = openAIClient.generateCompletion(prompt);
            TableStructure structure = parseTableStructure(response);

            return TableGenerationResult.builder()
                .tableName(tableName)
                .description(description)
                .fields(structure.getFields())
                .confidence(calculateConfidence(response))
                .build();

        } catch (Exception e) {
            throw new AIServiceException("AI生成表结构失败: " + e.getMessage());
        }
    }

    private String buildTableGenerationPrompt(String name, String description, String category) {
        return String.format("""
            请根据以下信息生成数据表结构：

            表名：%s
            描述：%s
            类别：%s

            请生成合适的字段列表，每个字段包含：
            - fieldName: 字段名（英文，下划线命名）
            - displayName: 显示名称（中文）
            - fieldType: 字段类型（text/number/date/boolean/select等）
            - isRequired: 是否必填
            - validationRules: 验证规则
            - description: 字段说明

            请以JSON格式返回，格式如下：
            {
              "fields": [
                {
                  "fieldName": "name",
                  "displayName": "姓名",
                  "fieldType": "text",
                  "isRequired": true,
                  "validationRules": {"maxLength": 50},
                  "description": "用户姓名"
                }
              ]
            }
            """, name, description, category);
    }
}
```

### 步骤4: 前端核心组件实现
```vue
<!-- TableDesigner.vue - 表设计器组件 -->
<template>
  <div class="table-designer">
    <div class="designer-header">
      <div class="table-info">
        <el-input
          v-model="tableInfo.name"
          placeholder="表名（英文）"
          class="table-name-input"
        />
        <el-input
          v-model="tableInfo.displayName"
          placeholder="显示名称"
          class="table-display-name-input"
        />
        <el-input
          v-model="tableInfo.description"
          type="textarea"
          placeholder="表描述"
          :rows="2"
          class="table-description-input"
        />
      </div>
      <div class="designer-actions">
        <el-button @click="showAIAssistant = true" type="primary">
          <el-icon><MagicStick /></el-icon>
          AI助手
        </el-button>
        <el-button @click="showTemplateSelector = true">
          <el-icon><Document /></el-icon>
          使用模板
        </el-button>
        <el-button @click="saveTable" type="success" :loading="saving">
          <el-icon><Check /></el-icon>
          保存表结构
        </el-button>
      </div>
    </div>

    <div class="designer-body">
      <div class="fields-panel">
        <div class="panel-header">
          <h3>字段列表</h3>
          <el-button @click="addField" type="primary" size="small">
            <el-icon><Plus /></el-icon>
            添加字段
          </el-button>
        </div>

        <draggable
          v-model="fields"
          @end="handleFieldReorder"
          item-key="id"
          class="field-list"
        >
          <template #item="{ element: field, index }">
            <div class="field-item" :class="{ active: selectedFieldIndex === index }">
              <div class="field-info" @click="selectField(index)">
                <div class="field-header">
                  <span class="field-name">{{ field.displayName || field.fieldName }}</span>
                  <el-tag :type="getFieldTypeColor(field.fieldType)" size="small">
                    {{ getFieldTypeText(field.fieldType) }}
                  </el-tag>
                </div>
                <div class="field-meta">
                  <el-icon v-if="field.isRequired" class="required-icon"><Star /></el-icon>
                  <el-icon v-if="field.isUnique" class="unique-icon"><Key /></el-icon>
                  <span class="field-type">{{ field.fieldName }}</span>
                </div>
              </div>
              <div class="field-actions">
                <el-button @click="editField(index)" size="small" type="text">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button @click="deleteField(index)" size="small" type="text">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
        </draggable>
      </div>

      <div class="field-editor-panel" v-if="selectedFieldIndex !== -1">
        <FieldEditor
          :field="fields[selectedFieldIndex]"
          @update="updateField"
          @generate-prompt="generateExtractionPrompt"
        />
      </div>
    </div>

    <!-- AI助手对话框 -->
    <AIAssistantDialog
      v-model="showAIAssistant"
      :table-info="tableInfo"
      @apply-suggestions="applySuggestions"
    />

    <!-- 模板选择器 -->
    <TemplateSelector
      v-model="showTemplateSelector"
      @apply-template="applyTemplate"
    />
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import draggable from 'vuedraggable'
import FieldEditor from './FieldEditor.vue'
import AIAssistantDialog from './AIAssistantDialog.vue'
import TemplateSelector from './TemplateSelector.vue'

export default {
  name: 'TableDesigner',
  components: {
    draggable,
    FieldEditor,
    AIAssistantDialog,
    TemplateSelector
  },
  setup() {
    const tableInfo = reactive({
      name: '',
      displayName: '',
      description: ''
    })

    const fields = ref([])
    const selectedFieldIndex = ref(-1)
    const showAIAssistant = ref(false)
    const showTemplateSelector = ref(false)
    const saving = ref(false)

    const addField = () => {
      fields.value.push({
        id: Date.now(),
        fieldName: `field_${fields.value.length + 1}`,
        displayName: '',
        fieldType: 'text',
        isRequired: false,
        isUnique: false,
        fieldOrder: fields.value.length,
        fieldOptions: {},
        validationRules: {},
        extractionPrompt: ''
      })
      selectedFieldIndex.value = fields.value.length - 1
    }

    const selectField = (index) => {
      selectedFieldIndex.value = index
    }

    const updateField = (updatedField) => {
      if (selectedFieldIndex.value !== -1) {
        fields.value[selectedFieldIndex.value] = { ...updatedField }
      }
    }

    const deleteField = (index) => {
      fields.value.splice(index, 1)
      if (selectedFieldIndex.value >= fields.value.length) {
        selectedFieldIndex.value = fields.value.length - 1
      }
    }

    const saveTable = async () => {
      saving.value = true
      try {
        const tableData = {
          ...tableInfo,
          fields: fields.value
        }
        await api.post(`/workspaces/${workspaceId}/tables`, tableData)
        ElMessage.success('表结构保存成功')
      } catch (error) {
        ElMessage.error('保存失败: ' + error.message)
      } finally {
        saving.value = false
      }
    }

    return {
      tableInfo,
      fields,
      selectedFieldIndex,
      showAIAssistant,
      showTemplateSelector,
      saving,
      addField,
      selectField,
      updateField,
      deleteField,
      saveTable
    }
  }
}
</script>
```

## 🧪 可视化验证指南

### 验证步骤1: 数据表管理
1. **创建数据表**
   - 进入工作空间，点击"数据表"菜单
   - 点击"创建数据表"按钮
   - 应该打开表设计器界面

2. **手动设计表结构**
   - 填写表名、显示名称、描述
   - 点击"添加字段"按钮
   - 配置字段名称、类型、验证规则
   - 应该能够拖拽调整字段顺序

3. **保存表结构**
   - 点击"保存表结构"按钮
   - 应该显示保存成功消息
   - 返回表列表应该看到新创建的表

### 验证步骤2: AI辅助功能
1. **AI生成表结构**
   - 在表设计器中点击"AI助手"
   - 输入表描述，如"员工信息管理表"
   - 点击"生成建议"
   - 应该返回包含姓名、工号、部门等字段的建议

2. **应用AI建议**
   - 查看AI生成的字段建议
   - 选择需要的字段点击"应用"
   - 字段应该自动添加到表设计器中

3. **AI生成提示词**
   - 选择一个字段，如"姓名"
   - 点击"生成抽取提示词"
   - 应该生成适合该字段的AI抽取提示词

### 验证步骤3: 字段类型验证
1. **文本字段**
   - 创建文本类型字段
   - 设置最大长度、正则验证
   - 保存后应该正确显示配置

2. **数字字段**
   - 创建数字类型字段
   - 设置最小值、最大值、精度
   - 应该支持整数和小数配置

3. **选择字段**
   - 创建选择类型字段
   - 添加多个选项值
   - 应该支持单选和多选模式

4. **日期字段**
   - 创建日期类型字段
   - 设置日期格式和范围
   - 应该支持日期和日期时间格式

### 验证步骤4: 模板系统
1. **使用预定义模板**
   - 点击"使用模板"按钮
   - 选择"客户信息"模板
   - 应该自动填充相关字段

2. **保存为模板**
   - 设计完表结构后
   - 点击"保存为模板"
   - 输入模板名称和描述
   - 应该保存成功并可供其他用户使用

## ✅ 验收标准

### 功能验收
- [x] 用户可以创建和编辑数据表
- [x] 支持多种字段类型（文本、数字、日期、选择等）
- [x] 可以设置字段验证规则
- [x] 支持字段拖拽排序
- [x] AI辅助生成表结构功能正常
- [x] 模板系统可用

### AI功能验收
- [x] AI可以根据描述生成合理的表结构
- [x] 生成的字段类型和验证规则合适
- [x] AI生成的提示词质量良好
- [x] AI服务响应时间在可接受范围内

### 用户体验验收
- [x] 表设计器界面直观易用
- [x] 字段编辑器功能完整
- [x] 拖拽操作流畅
- [x] 实时预览效果良好
- [x] 错误提示清晰明确

### 技术验收
- [x] 所有API接口测试通过
- [x] 数据库表结构设计合理
- [x] AI服务集成稳定可靠
- [x] 前端组件响应式设计完成
- [x] 性能测试满足要求