@echo off
chcp 65001 >nul
echo ========================================
echo Windows局域网发现和共享设置脚本
echo 本地IP: *************
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误：需要管理员权限运行此脚本
    echo 请右键点击命令提示符，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo [1/6] 启用网络发现...
netsh advfirewall firewall set rule group="网络发现" new enable=Yes
netsh advfirewall firewall set rule group="Network Discovery" new enable=Yes
if %errorLevel% equ 0 (
    echo ✓ 网络发现已启用
) else (
    echo ✗ 网络发现启用失败
)
echo.

echo [2/6] 启用文件和打印机共享...
netsh advfirewall firewall set rule group="文件和打印机共享" new enable=Yes
netsh advfirewall firewall set rule group="File and Printer Sharing" new enable=Yes
if %errorLevel% equ 0 (
    echo ✓ 文件和打印机共享已启用
) else (
    echo ✗ 文件和打印机共享启用失败
)
echo.

echo [3/6] 配置网络配置文件为专用网络...
REM 获取当前网络配置文件
for /f "tokens=3" %%i in ('netsh wlan show profiles ^| findstr "当前用户配置文件"') do set profile=%%i
if defined profile (
    netsh wlan set profileparameter name="%profile%" connectiontype=ESS connectionmode=auto
)
REM 设置网络为专用
powershell -Command "Get-NetConnectionProfile | Set-NetConnectionProfile -NetworkCategory Private"
if %errorLevel% equ 0 (
    echo ✓ 网络配置文件已设置为专用
) else (
    echo ✗ 网络配置文件设置失败
)
echo.

echo [4/6] 启用SMB服务...
sc config lanmanserver start= auto
net start server
if %errorLevel% equ 0 (
    echo ✓ SMB服务已启动
) else (
    echo ✗ SMB服务启动失败
)
echo.

echo [5/6] 配置防火墙规则...
REM 允许SMB流量
netsh advfirewall firewall add rule name="SMB-In" dir=in action=allow protocol=TCP localport=445
netsh advfirewall firewall add rule name="SMB-Out" dir=out action=allow protocol=TCP localport=445
REM 允许NetBIOS流量
netsh advfirewall firewall add rule name="NetBIOS-In" dir=in action=allow protocol=TCP localport=139
netsh advfirewall firewall add rule name="NetBIOS-Out" dir=out action=allow protocol=TCP localport=139
REM 允许ICMP ping
netsh advfirewall firewall add rule name="ICMP-In" dir=in action=allow protocol=icmpv4:8,any
echo ✓ 防火墙规则已配置
echo.

echo [6/6] 验证网络设置...
echo 正在检查网络连接...
ping -n 1 ************* >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ 网关连接正常
) else (
    echo ⚠ 网关连接可能有问题
)

echo.
echo ========================================
echo 配置完成！
echo ========================================
echo.
echo 当前计算机信息：
echo - 计算机名: %COMPUTERNAME%
echo - 本地IP: *************
echo - 网络路径: \\%COMPUTERNAME%
echo - IP路径: \\*************
echo.
echo 局域网内其他设备可通过以下方式访问：
echo 1. 在文件资源管理器地址栏输入: \\%COMPUTERNAME%
echo 2. 在文件资源管理器地址栏输入: \\*************
echo 3. 在运行对话框(Win+R)输入上述路径
echo.
echo 如需共享特定文件夹，请：
echo 1. 右键点击要共享的文件夹
echo 2. 选择"属性" → "共享"选项卡
echo 3. 点击"高级共享"并设置权限
echo.
echo 注意：确保所有设备都在同一局域网内(192.168.124.x)
echo.
pause