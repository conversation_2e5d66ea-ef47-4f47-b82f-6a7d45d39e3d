@echo off
chcp 65001 >nul
echo ========================================
echo AI-FDB项目文件夹共享设置
echo 本地IP: *************
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误：需要管理员权限运行此脚本
    echo 请右键点击命令提示符，选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 设置项目路径
set PROJECT_PATH=E:\AI-FDB
set SHARE_NAME=AI-FDB

echo 项目路径: %PROJECT_PATH%
echo 共享名称: %SHARE_NAME%
echo.

REM 检查项目路径是否存在
if not exist "%PROJECT_PATH%" (
    echo 错误：项目路径 %PROJECT_PATH% 不存在
    echo 请确认AI-FDB项目路径是否正确
    pause
    exit /b 1
)

echo [1/5] 删除已存在的共享（如果有）...
net share %SHARE_NAME% /delete >nul 2>&1
echo ✓ 清理完成
echo.

echo [2/5] 创建AI-FDB项目共享...
net share %SHARE_NAME%="%PROJECT_PATH%" /grant:everyone,change
if %errorLevel% equ 0 (
    echo ✓ 共享创建成功
) else (
    echo ✗ 共享创建失败
    pause
    exit /b 1
)
echo.

echo [3/5] 设置文件夹权限...
REM 给Everyone用户组添加修改权限
icacls "%PROJECT_PATH%" /grant Everyone:(OI)(CI)M >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ 文件夹权限设置成功
) else (
    echo ⚠ 文件夹权限设置可能失败，但共享仍可使用
)
echo.

echo [4/5] 启用网络发现和共享...
netsh advfirewall firewall set rule group="网络发现" new enable=Yes >nul 2>&1
netsh advfirewall firewall set rule group="文件和打印机共享" new enable=Yes >nul 2>&1
echo ✓ 网络发现和共享已启用
echo.

echo [5/5] 验证共享设置...
net share | findstr %SHARE_NAME% >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ 共享验证成功
) else (
    echo ✗ 共享验证失败
)
echo.

echo ========================================
echo 共享设置完成！
echo ========================================
echo.
echo AI-FDB项目现在可以通过以下方式访问：
echo.
echo 局域网访问路径：
echo - \\%COMPUTERNAME%\%SHARE_NAME%
echo - \\*************\%SHARE_NAME%
echo.
echo 访问方法：
echo 1. 在文件资源管理器地址栏输入上述路径
echo 2. 在运行对话框(Win+R)输入上述路径
echo 3. 在其他设备的文件管理器中输入路径
echo.
echo 权限说明：
echo - 所有用户都可以读取、写入和修改文件
echo - 可以创建新文件和文件夹
echo - 可以删除文件（请谨慎操作）
echo.
echo 测试命令：
echo 在本机测试: net view \\%COMPUTERNAME%
echo 在其他设备测试: net view \\*************
echo.
echo 注意事项：
echo - 确保所有设备都在192.168.124.x网段内
echo - 如果无法访问，请检查防火墙设置
echo - 建议定期备份重要文件
echo.

REM 显示当前所有共享
echo 当前所有共享列表：
net share
echo.

echo 如需取消共享，请运行：
echo net share %SHARE_NAME% /delete
echo.
pause