#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI-FDB 依赖安装脚本
安装PaddleOCR和其他必要的依赖包
"""

import subprocess
import sys
import os

def run_command(command, description):
    """执行命令并显示进度"""
    print(f"\n🔧 {description}")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} 完成")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        print(f"错误: {e.stderr}")
        return False

def main():
    """主安装函数"""
    print("🚀 AI-FDB 依赖安装程序")
    print("=" * 50)
    
    # 使用国内镜像源
    pip_mirror = "https://pypi.tuna.tsinghua.edu.cn/simple/"
    
    # 要安装的包列表
    packages = [
        "paddlepaddle",
        "paddleocr", 
        "requests",
        "pathlib",
        "opencv-python",
        "pillow"
    ]
    
    print(f"📦 将要安装的包: {', '.join(packages)}")
    print(f"🌐 使用镜像源: {pip_mirror}")
    
    # 升级pip
    if not run_command(f"python -m pip install --upgrade pip -i {pip_mirror}", "升级pip"):
        print("⚠️ pip升级失败，继续安装其他包...")
    
    # 安装每个包
    success_count = 0
    for package in packages:
        command = f"pip install {package} -i {pip_mirror}"
        if run_command(command, f"安装 {package}"):
            success_count += 1
    
    print(f"\n📊 安装结果:")
    print(f"✅ 成功安装: {success_count}/{len(packages)} 个包")
    
    if success_count == len(packages):
        print("\n🎉 所有依赖安装完成！")
        print("\n📋 下一步:")
        print("1. 运行测试程序: python test_ocr_semantic_extraction.py")
        print("2. 确保E:/OCR/real_test_files目录存在并包含测试文件")
    else:
        print(f"\n⚠️ 有 {len(packages) - success_count} 个包安装失败")
        print("请检查网络连接或手动安装失败的包")
    
    # 验证PaddleOCR安装
    print("\n🔍 验证PaddleOCR安装...")
    try:
        import paddleocr
        print("✅ PaddleOCR导入成功")
        
        # 尝试初始化PaddleOCR（这会下载模型）
        print("📥 初始化PaddleOCR（首次运行会下载模型）...")
        ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang='ch', use_gpu=False, show_log=False)
        print("✅ PaddleOCR初始化成功")
        
    except ImportError as e:
        print(f"❌ PaddleOCR导入失败: {e}")
    except Exception as e:
        print(f"⚠️ PaddleOCR初始化警告: {e}")
        print("这可能是首次运行，模型下载可能需要一些时间")

if __name__ == "__main__":
    main()
