# AI-FDB 项目简介

**AI-FDB（AI文件数据管理系统）** 是一个基于AI技术的文件数据管理系统，核心目标是将非结构化电子文件转换为结构化数据，并支持自然语言查询。

## 🎯 项目目标

- **智能数据抽取**: 使用AI技术从各类文档中自动提取结构化数据
- **自然语言查询**: 支持用自然语言查询和分析数据，自动生成SQL语句
- **智能数据可视化**: 根据查询结果自动推荐合适的图表类型
- **工作空间管理**: 提供多用户协作的工作空间环境
- **数据表设计**: 可视化的数据表结构设计和管理
- **批量处理**: 支持大批量文件的自动化处理

## 🛠️ 技术栈

- **后端**: Spring Boot 3.x, MySQL 8.0, MongoDB, Redis
- **前端**: Vue 3, Element Plus, Pinia, Vite, TypeScript
- **OCR引擎**: PaddleOCR PP-OCRv5_server_rec（全能力）
- **AI服务**: 阿里通义千问 qwen-turbo
- **AI问答**: Vanna AI框架 + ChromaDB向量数据库
- **数据可视化**: ECharts, Chart.js, Plotly
- **运维**: Docker, GitHub Actions

## 📋 开发计划

项目采用版本迭代开发，分为以下阶段：

- **v0.1** - 用户鉴权系统
- **v0.2** - AI核心模块（PaddleOCR + qwen-turbo）
- **v0.3** - 工作空间管理
- **v0.4** - 数据表创建
- **v0.5** - 数据记录录入与语义抽取
- **v0.6** - AI数据问答与可视化（Vanna AI + 智能图表）
- **v1.0** - 完整可运行系统

## 📚 文档索引

### 详细文档
- [详细任务文档](../tasks/detailed-tasks.md) - 完整的任务分解和实施细节
- [任务配置文件](../tasks/tasks.json) - 项目任务管理配置

### 版本文档
- [v0.1 用户鉴权系统](versions/v0.1/README.md)
- [v0.2 AI核心模块](versions/v0.2/README.md)
- [v0.3 工作空间管理](versions/v0.3/README.md)
- [v0.4 数据表创建](versions/v0.4/README.md)
- [v0.5 数据记录录入与语义抽取](versions/v0.5/README.md)
- [v0.6 AI数据问答与可视化](versions/v0.6/README.md)

### 部署文档
- [部署指南](deployment/README.md)
- [Windows域共享配置](deployment/windows-domain-sharing.md)

## 🚀 快速开始

1. **环境准备**: 参考 [详细任务文档](../tasks/detailed-tasks.md#1.3) 配置开发环境
2. **项目初始化**: 按照 [任务1](../tasks/detailed-tasks.md#任务-1项目初始化与环境配置) 创建项目结构
3. **开发流程**: 按版本顺序进行开发，详见各版本文档

## 📞 联系方式

- **项目团队**: AI-FDB Team
- **创建时间**: 2024-12-22
- **当前版本**: 0.1.0