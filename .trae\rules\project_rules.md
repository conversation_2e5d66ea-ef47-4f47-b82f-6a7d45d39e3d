### AI文件数据管理系统（AI-FDB）需求总结与功能模块设计文档


#### 一、需求总结
AI-FDB 是一个基于 AI 技术的文件数据管理系统，核心目标是将非结构化电子文件转换为结构化数据，并支持自然语言查询。系统具备以下核心能力：  
1. **非结构化数据处理**：通过 AI 从电子文件中抽取结构化数据，生成数据表。  
2. **自然语言交互**：支持用户通过自然语言查询数据表及原始文件内容。  
3. **多场景数据管理**：提供示例数据展示、用户自定义工作空间、数据表创建与记录录入功能，支持手动录入与 AI 批量抽取。  


#### 二、功能模块设计文档

### 1. 用户模块
**功能描述**：管理用户注册、登录及权限控制。  
#### 子功能：
- **用户认证**  
  - 注册功能：收集用户基本信息（邮箱/手机号、密码等），支持第三方登录（如微信、钉钉）。  
  - 登录功能：支持账号密码、验证码、扫码登录，记住登录状态（短期 cookie）。  
- **权限管理**  
  - 游客权限：仅可浏览数据中心示例表，无法引用或编辑。  
  - 注册用户权限：可引用数据表、创建工作空间及数据表、管理记录。  
  - 高级权限（可选）：支持团队协作时的成员角色分配（管理员、编辑者、查看者）。  


### 2. 数据中心模块
**功能描述**：展示公共示例数据表，提供引用入口。  
#### 子功能：
- **示例表展示**  
  - 分类浏览：按行业（金融、医疗、教育）、文件类型（合同、报告、简历）分类展示示例表。  
  - 详情查看：免登录状态下可查看表结构（字段名、类型）及脱敏示例数据。  
- **引用机制**  
  - 注册登录后，可“引用”示例表到个人工作空间，生成副本并支持自定义修改。  
  - 引用记录追踪：显示引用来源及版本信息，便于后续维护。  


### 3. 工作空间模块
**功能描述**：用户登录后的核心操作入口，管理个人数据资产。  
#### 子功能：
- **空间管理**  
  - 我的工作空间：默认展示用户创建或引用的所有工作空间，支持列表/网格视图切换。  
  - 新建空间：输入空间名称、可选备注（支持 AI 基于名称推荐生成，如输入“合同管理”，AI 生成“用于存储及管理公司各类业务合同的数据空间”）。  
  - 空间权限：支持设置空间为私有或共享（仅限团队版）。  
- **数据表管理**  
  - 表列表：展示当前空间内的所有数据表，标注创建时间、字段数、记录数。  
  - 快捷操作：支持对数据表进行重命名、复制、删除、导出（Excel/CSV）。  


### 4. 数据表模块
**功能描述**：支持数据表创建、字段设计及 AI 辅助配置。  
#### 子功能：
- **表创建**  
  - 手动创建：自定义表名、备注，手动添加字段（字段名、类型、是否必填）。  
  - AI 辅助创建：输入表名及备注（如“采购合同表”+“存储采购合同关键信息”），AI 自动生成字段（如供应商名称、合同金额、签约日期等）、字段类型（文本/数值/日期）及抽取提示词（如“从合同正文抽取供应商全称，格式为公司全称”）。  
- **表结构编辑**  
  - 字段修改：支持添加、删除、修改字段，调整字段顺序。  
  - 索引设置：为常用查询字段添加索引，提升查询效率。  
- **AI 配置**  
  - 抽取提示词管理：可编辑 AI 生成的提示词，优化非结构化数据抽取准确性。  
  - 字段映射规则：设置文件内容与字段的对应关系（如 PDF 合同中的“金额”字段映射到数据表“合同金额”）。  


### 5. 记录管理模块
**功能描述**：支持数据记录的添加、编辑、查询与批量处理。  
#### 子功能：
- **单条记录录入**  
  - 手动录入：打开表单界面，逐项填写字段内容，支持上传附件（如原始文件）。  
  - AI 抽取录入：上传单个文件（Word/PDF/Excel），AI 按数据表字段规则抽取内容并填充，用户可预览并修正抽取结果。  
- **批量记录录入**  
  - 批量上传：一次上传多份文件，系统自动识别文件类型并按对应数据表规则处理。  
  - 流式生成：AI 并行处理多文件，实时返回抽取结果，生成多条记录（如上传 100 份合同，系统逐份抽取并生成记录，支持中途暂停/重试）。  
- **记录查询与管理**  
  - 自然语言查询：输入“查找 2024 年金额大于 10 万的合同”，AI 解析语义并检索数据表及关联文件。  
  - 高级筛选：支持按字段组合（如日期范围、数值区间）筛选记录。  
  - 记录编辑与删除：支持修改单条记录内容，批量删除无效记录。  


### 6. AI 服务模块
**功能描述**：提供核心 AI 能力支持，贯穿系统各模块。  
#### 子功能：
- **自然语言处理（NLP）**  
  - 语义解析：处理用户查询语句，转换为数据表检索条件。  
  - 文本生成：生成工作空间备注、数据表字段提示词、抽取规则说明。  
- **非结构化数据抽取**  
  - 文档理解：识别 Word/PDF 等文件中的文本、表格、关键字段。  
  - 信息提取：按提示词规则抽取数据（如从合同中提取“甲方名称”“有效期”）。  
- **智能推荐**  
  - 字段推荐：根据表名与备注推荐合理的字段结构。  
  - 抽取规则优化：根据历史抽取结果自动调整提示词，提升准确率（如多次抽取“金额”失败后，AI 自动修改提示词为“提取数字金额，包含‘¥’或‘元’符号”）。  


### 三、模块交互流程示例
1. **游客浏览数据中心**：  
   游客访问系统 → 查看示例表 → 点击“引用”→ 跳转登录页 → 注册/登录后引用表到个人工作空间。  
2. **用户创建数据表**：  
   登录 → 进入工作空间 → 新建数据表 → 输入表名与备注 → AI 生成字段与提示词 → 用户调整字段结构 → 保存表。  
3. **批量录入数据**：  
   进入数据表 → 点击“批量录入”→ 上传多份文件 → AI 并行抽取 → 流式生成记录 → 用户预览并确认保存。  


### 四、核心技术要点
- **OCR引擎**：专门使用PaddleOCR PP-OCRv5_server_rec引擎，提供高精度的文字识别能力，支持中英文混合、表格识别、版面分析等全部功能。
- **AI模型选型**：使用阿里通义千问qwen-turbo模型进行语义理解和字段抽取，API Key: sk-beff2b8bc208457a9d971610488661f0。
- **语义抽取**：实现动态字段抽取功能，用户可自定义抽取字段（如"注册所有者"、"打印日期"等），通过语义理解而非硬编码实现。
- **数据安全**：用户文件加密存储，AI抽取过程中敏感信息脱敏处理。
- **性能优化**：批量处理时采用异步任务队列，流式生成减少用户等待时间。


### 五、数据库设计文档

#### 5.1 数据库架构
- **主数据库**：MySQL 8.0+，存储用户数据、工作空间、数据表结构、OCR识别结果、语义抽取记录等核心业务数据
- **文档存储**：MongoDB，存储原始文件内容、PaddleOCR识别结果、qwen-turbo抽取结果、文档元数据
- **缓存层**：Redis，缓存用户会话、热点数据、OCR和AI处理队列
- **文件存储**：本地文件系统或MinIO，存储上传的文档文件

#### 5.2 核心数据表设计

##### 5.2.1 用户相关表
```sql
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    status TINYINT DEFAULT 1 COMMENT '1:正常 0:禁用',
    role ENUM('guest', 'user', 'admin') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_phone (phone)
);

-- 用户会话表
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (session_token),
    INDEX idx_user_id (user_id)
);
```

##### 5.2.2 工作空间相关表
```sql
-- 工作空间表
CREATE TABLE workspaces (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_id BIGINT NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_owner_id (owner_id)
);

-- 工作空间成员表
CREATE TABLE workspace_members (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role ENUM('owner', 'admin', 'editor', 'viewer') DEFAULT 'viewer',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_workspace_user (workspace_id, user_id)
);
```

##### 5.2.3 数据表相关表
```sql
-- 数据表定义
CREATE TABLE data_tables (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    workspace_id BIGINT NOT NULL,
    table_schema JSON NOT NULL COMMENT '表结构定义',
    ai_config JSON COMMENT 'AI抽取配置',
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_workspace_id (workspace_id)
);

-- 数据表字段定义
CREATE TABLE table_fields (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    field_type ENUM('text', 'number', 'date', 'boolean', 'file') NOT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    field_order INT DEFAULT 0,
    extraction_prompt TEXT COMMENT 'AI抽取提示词',
    validation_rules JSON COMMENT '字段验证规则',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    UNIQUE KEY uk_table_field (table_id, field_name),
    INDEX idx_table_id (table_id)
);

-- 数据记录表
CREATE TABLE data_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT NOT NULL,
    record_data JSON NOT NULL COMMENT '记录数据',
    source_file_id BIGINT COMMENT '来源文件ID',
    extraction_confidence DECIMAL(3,2) COMMENT 'AI抽取置信度',
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_table_id (table_id),
    INDEX idx_created_at (created_at)
);
```

##### 5.2.4 文件管理表
```sql
-- 文件存储表
CREATE TABLE files (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    mime_type VARCHAR(100),
    md5_hash VARCHAR(32) NOT NULL,
    uploaded_by BIGINT NOT NULL,
    workspace_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES users(id),
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE SET NULL,
    INDEX idx_md5_hash (md5_hash),
    INDEX idx_workspace_id (workspace_id)
);

-- AI处理任务表
CREATE TABLE ai_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_type ENUM('extract', 'query', 'generate') NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    input_data JSON NOT NULL,
    output_data JSON,
    error_message TEXT,
    processing_time INT COMMENT '处理时间(秒)',
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by)
);
```

#### 5.3 MongoDB文档结构
```javascript
// 文档内容集合
db.document_contents = {
    _id: ObjectId,
    file_id: Number, // 关联files表
    content_type: String, // 'text', 'image', 'table'
    raw_content: String, // 原始文本内容
    structured_content: Object, // 结构化内容
    ocr_result: Object, // OCR识别结果
    ai_analysis: Object, // AI分析结果
    created_at: Date,
    updated_at: Date
};

// AI抽取历史
db.extraction_history = {
    _id: ObjectId,
    task_id: Number, // 关联ai_tasks表
    file_id: Number,
    table_id: Number,
    extraction_rules: Object,
    extracted_data: Object,
    confidence_scores: Object,
    created_at: Date
};
```


### 六、后端设计文档

#### 6.1 技术架构
- **框架**：Spring Boot 3.x + Spring Security + Spring Data JPA
- **数据库**：MySQL 8.0 + MongoDB + Redis
- **消息队列**：Redis + Spring Boot Async（处理OCR和AI任务队列）
- **文件存储**：本地存储 + MinIO（可选）
- **OCR服务**：PaddleOCR PP-OCRv5_server_rec引擎
- **AI服务**：阿里通义千问qwen-turbo模型

#### 6.2 项目结构
```
src/main/java/com/aifdb/
├── config/          # 配置类
│   ├── SecurityConfig.java
│   ├── DatabaseConfig.java
│   ├── RedisConfig.java
│   └── AIServiceConfig.java
├── controller/      # 控制器层
│   ├── AuthController.java
│   ├── WorkspaceController.java
│   ├── DataTableController.java
│   ├── RecordController.java
│   └── FileController.java
├── service/         # 服务层
│   ├── UserService.java
│   ├── WorkspaceService.java
│   ├── DataTableService.java
│   ├── RecordService.java
│   ├── FileService.java
│   └── AIService.java
├── repository/      # 数据访问层
│   ├── UserRepository.java
│   ├── WorkspaceRepository.java
│   ├── DataTableRepository.java
│   └── RecordRepository.java
├── entity/          # 实体类
│   ├── User.java
│   ├── Workspace.java
│   ├── DataTable.java
│   └── Record.java
├── dto/             # 数据传输对象
│   ├── request/
│   └── response/
├── utils/           # 工具类
│   ├── JwtUtils.java
│   ├── FileUtils.java
│   └── AIUtils.java
└── exception/       # 异常处理
    ├── GlobalExceptionHandler.java
    └── CustomExceptions.java
```

#### 6.3 核心API设计

##### 6.3.1 用户认证API
```java
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    
    @PostMapping("/register")
    public ResponseEntity<AuthResponse> register(@RequestBody RegisterRequest request);
    
    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@RequestBody LoginRequest request);
    
    @PostMapping("/logout")
    public ResponseEntity<Void> logout(@RequestHeader("Authorization") String token);
    
    @PostMapping("/refresh")
    public ResponseEntity<AuthResponse> refreshToken(@RequestBody RefreshTokenRequest request);
}
```

##### 6.3.2 工作空间API
```java
@RestController
@RequestMapping("/api/workspaces")
public class WorkspaceController {
    
    @GetMapping
    public ResponseEntity<PageResponse<WorkspaceDTO>> getWorkspaces(
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "10") int size);
    
    @PostMapping
    public ResponseEntity<WorkspaceDTO> createWorkspace(@RequestBody CreateWorkspaceRequest request);
    
    @GetMapping("/{id}")
    public ResponseEntity<WorkspaceDTO> getWorkspace(@PathVariable Long id);
    
    @PutMapping("/{id}")
    public ResponseEntity<WorkspaceDTO> updateWorkspace(
        @PathVariable Long id, 
        @RequestBody UpdateWorkspaceRequest request);
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteWorkspace(@PathVariable Long id);
}
```

##### 6.3.3 数据表API
```java
@RestController
@RequestMapping("/api/workspaces/{workspaceId}/tables")
public class DataTableController {
    
    @GetMapping
    public ResponseEntity<List<DataTableDTO>> getTables(@PathVariable Long workspaceId);
    
    @PostMapping
    public ResponseEntity<DataTableDTO> createTable(
        @PathVariable Long workspaceId,
        @RequestBody CreateTableRequest request);
    
    @PostMapping("/ai-generate")
    public ResponseEntity<DataTableDTO> generateTableByAI(
        @PathVariable Long workspaceId,
        @RequestBody AIGenerateTableRequest request);
    
    @GetMapping("/{tableId}")
    public ResponseEntity<DataTableDTO> getTable(
        @PathVariable Long workspaceId,
        @PathVariable Long tableId);
    
    @PutMapping("/{tableId}")
    public ResponseEntity<DataTableDTO> updateTable(
        @PathVariable Long workspaceId,
        @PathVariable Long tableId,
        @RequestBody UpdateTableRequest request);
}
```

##### 6.3.4 记录管理API
```java
@RestController
@RequestMapping("/api/tables/{tableId}/records")
public class RecordController {
    
    @GetMapping
    public ResponseEntity<PageResponse<RecordDTO>> getRecords(
        @PathVariable Long tableId,
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "20") int size,
        @RequestParam(required = false) String search);
    
    @PostMapping
    public ResponseEntity<RecordDTO> createRecord(
        @PathVariable Long tableId,
        @RequestBody CreateRecordRequest request);
    
    @PostMapping("/batch")
    public ResponseEntity<BatchProcessResponse> batchCreateRecords(
        @PathVariable Long tableId,
        @RequestParam("files") MultipartFile[] files);
    
    @PostMapping("/ai-extract")
    public ResponseEntity<RecordDTO> extractRecordFromFile(
        @PathVariable Long tableId,
        @RequestParam("file") MultipartFile file);
    
    @PostMapping("/query")
    public ResponseEntity<List<RecordDTO>> queryRecords(
        @PathVariable Long tableId,
        @RequestBody NaturalLanguageQueryRequest request);
}
```

#### 6.4 AI服务集成
```java
@Service
public class AIService {
    
    @Autowired
    private OpenAIClient openAIClient;
    
    @Autowired
    private BaiduAIClient baiduAIClient;
    
    /**
     * 从文档中抽取结构化数据
     */
    public ExtractionResult extractDataFromDocument(
        String documentContent, 
        List<FieldDefinition> fields) {
        // 构建提示词
        String prompt = buildExtractionPrompt(documentContent, fields);
        
        // 调用AI模型
        AIResponse response = openAIClient.chat(prompt);
        
        // 解析结果
        return parseExtractionResult(response.getContent(), fields);
    }
    
    /**
     * 自然语言查询转SQL
     */
    public QueryResult parseNaturalLanguageQuery(
        String query, 
        TableSchema schema) {
        String prompt = buildQueryPrompt(query, schema);
        AIResponse response = openAIClient.chat(prompt);
        return parseQueryResult(response.getContent());
    }
    
    /**
     * 生成数据表结构
     */
    public TableGenerationResult generateTableStructure(
        String tableName, 
        String description) {
        String prompt = buildTableGenerationPrompt(tableName, description);
        AIResponse response = openAIClient.chat(prompt);
        return parseTableStructure(response.getContent());
    }
}
```

#### 6.5 安全配置
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf(csrf -> csrf.disable())
            .sessionManagement(session -> 
                session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/public/**").permitAll()
                .anyRequest().authenticated()
            )
            .addFilterBefore(jwtAuthenticationFilter(), 
                UsernamePasswordAuthenticationFilter.class);
        
        return http.build();
    }
    
    @Bean
    public JwtAuthenticationFilter jwtAuthenticationFilter() {
        return new JwtAuthenticationFilter();
    }
}
```


### 七、前端设计文档

#### 7.1 技术架构
- **框架**：Vue 3 + TypeScript + Vite
- **UI组件库**：Element Plus
- **状态管理**：Pinia
- **路由**：Vue Router 4
- **HTTP客户端**：Axios
- **图表库**：ECharts
- **富文本编辑器**：TinyMCE
- **文件上传**：vue-upload-component

#### 7.2 项目结构
```
src/
├── assets/          # 静态资源
│   ├── images/
│   ├── icons/
│   └── styles/
├── components/      # 公共组件
│   ├── common/      # 通用组件
│   │   ├── Header.vue
│   │   ├── Sidebar.vue
│   │   └── Loading.vue
│   ├── forms/       # 表单组件
│   │   ├── TableForm.vue
│   │   └── RecordForm.vue
│   └── charts/      # 图表组件
│       └── DataChart.vue
├── views/           # 页面组件
│   ├── auth/        # 认证页面
│   │   ├── Login.vue
│   │   └── Register.vue
│   ├── dashboard/   # 仪表板
│   │   └── Dashboard.vue
│   ├── workspace/   # 工作空间
│   │   ├── WorkspaceList.vue
│   │   ├── WorkspaceDetail.vue
│   │   └── CreateWorkspace.vue
│   ├── table/       # 数据表
│   │   ├── TableList.vue
│   │   ├── TableDetail.vue
│   │   ├── CreateTable.vue
│   │   └── TableDesigner.vue
│   ├── record/      # 记录管理
│   │   ├── RecordList.vue
│   │   ├── RecordDetail.vue
│   │   ├── BatchUpload.vue
│   │   └── QueryInterface.vue
│   └── datacenter/  # 数据中心
│       ├── ExampleTables.vue
│       └── TablePreview.vue
├── stores/          # 状态管理
│   ├── auth.ts
│   ├── workspace.ts
│   ├── table.ts
│   └── record.ts
├── router/          # 路由配置
│   └── index.ts
├── api/             # API接口
│   ├── auth.ts
│   ├── workspace.ts
│   ├── table.ts
│   └── record.ts
├── utils/           # 工具函数
│   ├── request.ts
│   ├── auth.ts
│   └── file.ts
├── types/           # TypeScript类型定义
│   ├── auth.ts
│   ├── workspace.ts
│   └── table.ts
└── App.vue
```

#### 7.3 核心页面设计

##### 7.3.1 登录页面 (Login.vue)
```vue
<template>
  <div class="login-container">
    <el-card class="login-card">
      <h2>AI文件数据管理系统</h2>
      <el-form :model="loginForm" :rules="rules" ref="loginFormRef">
        <el-form-item prop="email">
          <el-input 
            v-model="loginForm.email" 
            placeholder="邮箱"
            prefix-icon="Message"
          />
        </el-form-item>
        <el-form-item prop="password">
          <el-input 
            v-model="loginForm.password" 
            type="password"
            placeholder="密码"
            prefix-icon="Lock"
            show-password
          />
        </el-form-item>
        <el-form-item>
          <el-button 
            type="primary" 
            @click="handleLogin"
            :loading="loading"
            class="login-btn"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
      <div class="login-footer">
        <router-link to="/register">还没有账号？立即注册</router-link>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import type { LoginForm } from '@/types/auth'

const router = useRouter()
const authStore = useAuthStore()
const loading = ref(false)
const loginFormRef = ref()

const loginForm = reactive<LoginForm>({
  email: '',
  password: ''
})

const rules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  try {
    await loginFormRef.value.validate()
    loading.value = true
    await authStore.login(loginForm)
    router.push('/dashboard')
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}
</script>
```

##### 7.3.2 工作空间列表 (WorkspaceList.vue)
```vue
<template>
  <div class="workspace-list">
    <div class="header">
      <h2>我的工作空间</h2>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新建工作空间
      </el-button>
    </div>
    
    <div class="workspace-grid">
      <el-card 
        v-for="workspace in workspaces" 
        :key="workspace.id"
        class="workspace-card"
        @click="enterWorkspace(workspace.id)"
      >
        <div class="workspace-info">
          <h3>{{ workspace.name }}</h3>
          <p>{{ workspace.description }}</p>
          <div class="workspace-meta">
            <span>{{ workspace.tableCount }} 个数据表</span>
            <span>{{ formatDate(workspace.updatedAt) }}</span>
          </div>
        </div>
        <div class="workspace-actions">
          <el-dropdown @command="handleCommand">
            <el-icon><MoreFilled /></el-icon>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="{action: 'edit', id: workspace.id}">
                  编辑
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'delete', id: workspace.id}">
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-card>
    </div>
    
    <!-- 创建工作空间对话框 -->
    <CreateWorkspaceDialog 
      v-model="showCreateDialog"
      @created="handleWorkspaceCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useWorkspaceStore } from '@/stores/workspace'
import CreateWorkspaceDialog from '@/components/workspace/CreateWorkspaceDialog.vue'
import type { Workspace } from '@/types/workspace'

const router = useRouter()
const workspaceStore = useWorkspaceStore()
const showCreateDialog = ref(false)
const workspaces = ref<Workspace[]>([])

onMounted(async () => {
  await loadWorkspaces()
})

const loadWorkspaces = async () => {
  try {
    workspaces.value = await workspaceStore.getWorkspaces()
  } catch (error) {
    console.error('加载工作空间失败:', error)
  }
}

const enterWorkspace = (id: number) => {
  router.push(`/workspace/${id}`)
}

const handleWorkspaceCreated = () => {
  showCreateDialog.value = false
  loadWorkspaces()
}

const handleCommand = async (command: {action: string, id: number}) => {
  if (command.action === 'delete') {
    await workspaceStore.deleteWorkspace(command.id)
    loadWorkspaces()
  }
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString()
}
</script>
```

##### 7.3.3 数据表设计器 (TableDesigner.vue)
```vue
<template>
  <div class="table-designer">
    <div class="designer-header">
      <h2>{{ isEdit ? '编辑数据表' : '创建数据表' }}</h2>
      <div class="actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </div>
    
    <div class="designer-content">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card title="基本信息">
            <el-form :model="tableForm" label-width="80px">
              <el-form-item label="表名">
                <el-input v-model="tableForm.name" placeholder="请输入表名" />
              </el-form-item>
              <el-form-item label="描述">
                <el-input 
                  v-model="tableForm.description" 
                  type="textarea" 
                  :rows="3"
                  placeholder="请输入表描述"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="success" @click="generateByAI">
                  <el-icon><MagicStick /></el-icon>
                  AI生成字段
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
        
        <el-col :span="16">
          <el-card title="字段设计">
            <div class="field-list">
              <div class="field-header">
                <el-button type="primary" size="small" @click="addField">
                  添加字段
                </el-button>
              </div>
              
              <el-table :data="tableForm.fields" border>
                <el-table-column label="字段名" width="150">
                  <template #default="{row, $index}">
                    <el-input v-model="row.name" placeholder="字段名" />
                  </template>
                </el-table-column>
                
                <el-table-column label="类型" width="120">
                  <template #default="{row}">
                    <el-select v-model="row.type">
                      <el-option label="文本" value="text" />
                      <el-option label="数字" value="number" />
                      <el-option label="日期" value="date" />
                      <el-option label="布尔" value="boolean" />
                      <el-option label="文件" value="file" />
                    </el-select>
                  </template>
                </el-table-column>
                
                <el-table-column label="必填" width="80">
                  <template #default="{row}">
                    <el-checkbox v-model="row.required" />
                  </template>
                </el-table-column>
                
                <el-table-column label="AI提示词">
                  <template #default="{row}">
                    <el-input 
                      v-model="row.extractionPrompt" 
                      placeholder="AI抽取提示词"
                    />
                  </template>
                </el-table-column>
                
                <el-table-column label="操作" width="100">
                  <template #default="{$index}">
                    <el-button 
                      type="danger" 
                      size="small" 
                      @click="removeField($index)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTableStore } from '@/stores/table'
import type { TableForm, FieldDefinition } from '@/types/table'

const route = useRoute()
const router = useRouter()
const tableStore = useTableStore()
const isEdit = ref(false)

const tableForm = reactive<TableForm>({
  name: '',
  description: '',
  fields: []
})

onMounted(async () => {
  if (route.params.tableId) {
    isEdit.value = true
    await loadTable(Number(route.params.tableId))
  }
})

const loadTable = async (tableId: number) => {
  try {
    const table = await tableStore.getTable(tableId)
    Object.assign(tableForm, table)
  } catch (error) {
    console.error('加载数据表失败:', error)
  }
}

const addField = () => {
  tableForm.fields.push({
    name: '',
    type: 'text',
    required: false,
    extractionPrompt: ''
  })
}

const removeField = (index: number) => {
  tableForm.fields.splice(index, 1)
}

const generateByAI = async () => {
  try {
    const fields = await tableStore.generateFieldsByAI({
      name: tableForm.name,
      description: tableForm.description
    })
    tableForm.fields = fields
  } catch (error) {
    console.error('AI生成字段失败:', error)
  }
}

const handleSave = async () => {
  try {
    if (isEdit.value) {
      await tableStore.updateTable(Number(route.params.tableId), tableForm)
    } else {
      await tableStore.createTable(Number(route.params.workspaceId), tableForm)
    }
    router.back()
  } catch (error) {
    console.error('保存失败:', error)
  }
}

const handleCancel = () => {
  router.back()
}
</script>
```

#### 7.4 状态管理 (Pinia Store)
```typescript
// stores/auth.ts
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login as apiLogin, logout as apiLogout } from '@/api/auth'
import type { LoginForm, User } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  
  const login = async (loginForm: LoginForm) => {
    const response = await apiLogin(loginForm)
    user.value = response.user
    token.value = response.token
    localStorage.setItem('token', response.token)
  }
  
  const logout = async () => {
    await apiLogout()
    user.value = null
    token.value = null
    localStorage.removeItem('token')
  }
  
  const isAuthenticated = computed(() => !!token.value)
  
  return {
    user,
    token,
    login,
    logout,
    isAuthenticated
  }
})

// stores/table.ts
import { defineStore } from 'pinia'
import { ref } from 'vue'
import * as tableApi from '@/api/table'
import type { DataTable, TableForm } from '@/types/table'

export const useTableStore = defineStore('table', () => {
  const tables = ref<DataTable[]>([])
  const currentTable = ref<DataTable | null>(null)
  
  const getTables = async (workspaceId: number) => {
    const response = await tableApi.getTables(workspaceId)
    tables.value = response
    return response
  }
  
  const createTable = async (workspaceId: number, tableForm: TableForm) => {
    const response = await tableApi.createTable(workspaceId, tableForm)
    tables.value.push(response)
    return response
  }
  
  const generateFieldsByAI = async (params: {name: string, description: string}) => {
    return await tableApi.generateFieldsByAI(params)
  }
  
  return {
    tables,
    currentTable,
    getTables,
    createTable,
    generateFieldsByAI
  }
})
```

#### 7.5 路由配置
```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue')
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue')
  },
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/dashboard/Dashboard.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/workspaces',
    name: 'WorkspaceList',
    component: () => import('@/views/workspace/WorkspaceList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/workspace/:id',
    name: 'WorkspaceDetail',
    component: () => import('@/views/workspace/WorkspaceDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/workspace/:workspaceId/table/create',
    name: 'CreateTable',
    component: () => import('@/views/table/TableDesigner.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/table/:tableId',
    name: 'TableDetail',
    component: () => import('@/views/table/TableDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/datacenter',
    name: 'DataCenter',
    component: () => import('@/views/datacenter/ExampleTables.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else {
    next()
  }
})

export default router
```