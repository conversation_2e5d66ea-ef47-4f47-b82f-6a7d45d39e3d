#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI-FDB OCR和语义抽取测试程序
测试PaddleOCR PP-OCRv5_server_rec引擎和qwen-turbo模型的集成
"""

import os
import sys
import json
import time
import requests
from pathlib import Path
from paddleocr import PaddleOCR
from typing import Dict, List, Any, Optional

class OCRSemanticExtractor:
    """OCR和语义抽取集成类"""
    
    def __init__(self):
        """初始化OCR和AI服务"""
        # 初始化PaddleOCR
        self.ocr = PaddleOCR(
            use_angle_cls=True,
            lang='ch',
            use_gpu=False,
            show_log=False,
            det_model_dir=None,  # 使用默认PP-OCRv4_server_det
            rec_model_dir=None,  # 使用默认PP-OCRv5_server_rec
            cls_model_dir=None,  # 使用默认分类模型
            use_space_char=True
        )
        
        # qwen-turbo配置
        self.qwen_api_key = "sk-beff2b8bc208457a9d971610488661f0"
        self.qwen_base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        
        print("✅ OCR和AI服务初始化完成")
        print(f"📋 PaddleOCR版本: PP-OCRv5_server_rec")
        print(f"🤖 AI模型: qwen-turbo")
    
    def ocr_extract_text(self, image_path: str) -> Dict[str, Any]:
        """使用PaddleOCR提取文本"""
        print(f"\n🔍 开始OCR识别: {image_path}")
        
        start_time = time.time()
        
        try:
            # 执行OCR识别
            result = self.ocr.ocr(image_path, cls=True)
            
            # 处理识别结果
            text_blocks = []
            full_text_lines = []
            total_confidence = 0
            valid_blocks = 0
            
            if result and result[0]:
                for line in result[0]:
                    if line and len(line) >= 2:
                        bbox = line[0]  # 边界框坐标
                        text_info = line[1]  # (文本, 置信度)
                        
                        if text_info and len(text_info) >= 2:
                            text = text_info[0]
                            confidence = float(text_info[1])
                            
                            text_blocks.append({
                                'text': text,
                                'confidence': confidence,
                                'bbox': bbox
                            })
                            
                            full_text_lines.append(text)
                            total_confidence += confidence
                            valid_blocks += 1
            
            # 计算平均置信度
            avg_confidence = total_confidence / valid_blocks if valid_blocks > 0 else 0
            processing_time = time.time() - start_time
            
            ocr_result = {
                'success': True,
                'engine': 'PaddleOCR',
                'version': 'PP-OCRv5_server_rec',
                'file_path': image_path,
                'text_blocks': text_blocks,
                'full_text': '\n'.join(full_text_lines),
                'block_count': len(text_blocks),
                'average_confidence': round(avg_confidence, 3),
                'processing_time': round(processing_time, 2)
            }
            
            print(f"✅ OCR识别完成，识别到 {len(text_blocks)} 个文本块")
            print(f"⏱️ 处理时间: {processing_time:.2f}秒")
            print(f"📊 平均置信度: {avg_confidence:.3f}")
            
            return ocr_result
            
        except Exception as e:
            print(f"❌ OCR识别失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'file_path': image_path
            }
    
    def semantic_extract_fields(self, text: str, fields: List[Dict[str, str]]) -> Dict[str, Any]:
        """使用qwen-turbo进行语义字段抽取"""
        print(f"\n🧠 开始语义抽取，目标字段: {[f['name'] for f in fields]}")
        
        start_time = time.time()
        
        try:
            # 构建提示词
            field_descriptions = []
            for field in fields:
                field_descriptions.append(f"- {field['name']}: {field['description']}")
            
            prompt = f"""请从以下文本中抽取指定的字段信息：

文本内容：
{text}

需要抽取的字段：
{chr(10).join(field_descriptions)}

请以JSON格式返回抽取结果，格式如下：
{{
    "字段名1": "抽取的值1",
    "字段名2": "抽取的值2",
    "confidence": {{
        "字段名1": 0.95,
        "字段名2": 0.88
    }}
}}

如果某个字段在文本中找不到，请返回null。置信度请根据抽取的准确性给出0-1之间的数值。"""

            # 调用qwen-turbo API
            headers = {
                'Authorization': f'Bearer {self.qwen_api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                "model": "qwen-turbo",
                "input": {
                    "messages": [
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ]
                },
                "parameters": {
                    "max_tokens": 2000,
                    "temperature": 0.3
                }
            }
            
            response = requests.post(
                self.qwen_base_url,
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result['output']['text']
                
                # 尝试解析JSON结果
                try:
                    # 提取JSON部分
                    json_start = ai_response.find('{')
                    json_end = ai_response.rfind('}') + 1
                    if json_start >= 0 and json_end > json_start:
                        json_str = ai_response[json_start:json_end]
                        extracted_data = json.loads(json_str)
                    else:
                        extracted_data = {"error": "无法解析AI返回的JSON格式"}
                        
                except json.JSONDecodeError:
                    extracted_data = {"error": "AI返回的不是有效的JSON格式", "raw_response": ai_response}
                
                processing_time = time.time() - start_time
                
                semantic_result = {
                    'success': True,
                    'model': 'qwen-turbo',
                    'extracted_data': extracted_data,
                    'raw_response': ai_response,
                    'processing_time': round(processing_time, 2)
                }
                
                print(f"✅ 语义抽取完成")
                print(f"⏱️ 处理时间: {processing_time:.2f}秒")
                
                return semantic_result
                
            else:
                print(f"❌ qwen-turbo API调用失败: {response.status_code}")
                return {
                    'success': False,
                    'error': f"API调用失败: {response.status_code}",
                    'response': response.text
                }
                
        except Exception as e:
            print(f"❌ 语义抽取失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def process_document(self, file_path: str, extraction_fields: List[Dict[str, str]]) -> Dict[str, Any]:
        """处理单个文档：OCR + 语义抽取"""
        print(f"\n📄 处理文档: {file_path}")
        
        # 步骤1: OCR识别
        ocr_result = self.ocr_extract_text(file_path)
        
        if not ocr_result['success']:
            return {
                'success': False,
                'file_path': file_path,
                'error': 'OCR识别失败',
                'ocr_result': ocr_result
            }
        
        # 步骤2: 语义抽取
        semantic_result = self.semantic_extract_fields(
            ocr_result['full_text'], 
            extraction_fields
        )
        
        # 合并结果
        final_result = {
            'success': semantic_result['success'],
            'file_path': file_path,
            'ocr_result': ocr_result,
            'semantic_result': semantic_result,
            'total_processing_time': ocr_result.get('processing_time', 0) + semantic_result.get('processing_time', 0)
        }
        
        return final_result

def main():
    """主测试函数"""
    print("🚀 AI-FDB OCR和语义抽取测试程序")
    print("=" * 50)
    
    # 初始化抽取器
    extractor = OCRSemanticExtractor()
    
    # 定义要抽取的字段
    extraction_fields = [
        {
            "name": "注册所有者",
            "description": "文档中的注册所有者、持有人或业主的姓名"
        },
        {
            "name": "打印日期", 
            "description": "文档的打印日期、生成日期或文档日期"
        },
        {
            "name": "证件号码",
            "description": "身份证号码、营业执照号码或其他证件号码"
        },
        {
            "name": "金额",
            "description": "文档中的金额、价格或费用数值"
        }
    ]
    
    # 测试文件目录
    test_files_dir = Path("E:/OCR/real_test_files")
    
    if not test_files_dir.exists():
        print(f"❌ 测试文件目录不存在: {test_files_dir}")
        print("请确保E:/OCR/real_test_files目录存在并包含测试文件")
        return
    
    # 支持的文件格式
    supported_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.pdf']
    
    # 查找测试文件
    test_files = []
    for ext in supported_extensions:
        test_files.extend(test_files_dir.glob(f"*{ext}"))
        test_files.extend(test_files_dir.glob(f"*{ext.upper()}"))
    
    if not test_files:
        print(f"❌ 在 {test_files_dir} 中未找到支持的测试文件")
        print(f"支持的格式: {', '.join(supported_extensions)}")
        return
    
    print(f"📁 找到 {len(test_files)} 个测试文件")
    
    # 处理每个测试文件
    results = []
    for i, file_path in enumerate(test_files[:3], 1):  # 限制测试前3个文件
        print(f"\n{'='*60}")
        print(f"📋 测试 {i}/{min(3, len(test_files))}: {file_path.name}")
        print(f"{'='*60}")
        
        result = extractor.process_document(str(file_path), extraction_fields)
        results.append(result)
        
        # 显示结果
        if result['success']:
            print(f"\n✅ 文档处理成功")
            print(f"⏱️ 总处理时间: {result['total_processing_time']:.2f}秒")
            
            if 'extracted_data' in result['semantic_result']:
                print(f"\n📊 抽取结果:")
                extracted = result['semantic_result']['extracted_data']
                for field_name, value in extracted.items():
                    if field_name != 'confidence':
                        confidence = extracted.get('confidence', {}).get(field_name, 'N/A')
                        print(f"  • {field_name}: {value} (置信度: {confidence})")
        else:
            print(f"❌ 文档处理失败: {result.get('error', '未知错误')}")
    
    # 保存测试结果
    results_file = Path("test_results.json")
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 测试结果已保存到: {results_file}")
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
